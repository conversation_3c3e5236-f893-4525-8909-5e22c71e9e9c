using UnityEngine;
using System;

namespace ParticleSystemToGfxFramework.Data
{
    /// <summary>
    /// 粒子系统验证配置，支持配置化的验证规则
    /// </summary>
    [CreateAssetMenu(fileName = "ConversionRules", menuName = "ParticleToGfx/Validation Config")]
    public class ValidationConfig : ScriptableObject
    {
        [Header("配置信息")]
        [SerializeField] private string version = "1.0";
        [SerializeField] private string description = "粒子系统转换验证规则配置";
        
        [Header("验证规则")]
        [SerializeField] private ExpressionRule[] rules = new ExpressionRule[0];
        
        /// <summary>
        /// 配置版本
        /// </summary>
        public string Version => version;
        
        /// <summary>
        /// 配置描述
        /// </summary>
        public string Description => description;
        
        /// <summary>
        /// 验证规则
        /// </summary>
        public ExpressionRule[] Rules => rules;
        
        /// <summary>
        /// 验证配置是否有效
        /// </summary>
        public bool IsValid()
        {
            // 检查是否至少有一个验证规则
            return rules.Length > 0;
        }

        /// <summary>
        /// 获取所有规则的总数
        /// </summary>
        public int GetTotalRuleCount()
        {
            return rules.Length;
        }
        
        /// <summary>
        /// 创建默认配置
        /// </summary>
        public static ValidationConfig CreateDefault()
        {
            var config = CreateInstance<ValidationConfig>();
            config.version = "1.1";
            config.description = "默认粒子系统转换验证规则 - 增加了更多精细的验证条件";

            // 统一使用表达式规则
            config.rules = new ExpressionRule[]
            {
                new ExpressionRule
                {
                    name = "maxParticles",
                    description = "检查粒子最大数量必须为1",
                    expression = "main.maxParticles == 1",
                    severity = ValidationSeverity.Error,
                    errorMessage = "Max Particles必须等于1"
                },
                new ExpressionRule
                {
                    name = "renderModeAndMesh",
                    description = "检查渲染模式必须为Mesh且Mesh不为空",
                    expression = "renderer.renderMode == 4 && renderer.mesh != null",
                    severity = ValidationSeverity.Error,
                    errorMessage = "必须使用Mesh渲染模式且Mesh不为空"
                },
                new ExpressionRule
                {
                    name = "forbiddenModules",
                    description = "检查禁用模块未启用",
                    expression = "!shape.enabled && !velocityOverLifetime.enabled && !limitVelocityOverLifetime.enabled && !inheritVelocity.enabled && !forceOverLifetime.enabled && !colorBySpeed.enabled && !sizeBySpeed.enabled && !rotationBySpeed.enabled && !externalForces.enabled && !noise.enabled && !collision.enabled && !trigger.enabled && !subEmitters.enabled && !textureSheetAnimation.enabled && !lights.enabled && !trails.enabled",
                    severity = ValidationSeverity.Error,
                    errorMessage = "不能启用以下禁用模块: Shape, Velocity over Lifetime, Limit Velocity over Lifetime, Inherit Velocity, Force over Lifetime, Color by Speed, Size by Speed, Rotation by Speed, External Forces, Noise, Collision, Triggers, Sub Emitters, Texture Sheet Animation, Lights, Trails"
                },
                new ExpressionRule
                {
                    name = "prewarmDisabled",
                    description = "检查Prewarm必须为关闭",
                    expression = "main.prewarm == false",
                    severity = ValidationSeverity.Error,
                    errorMessage = "Prewarm必须为关闭状态"
                },
                new ExpressionRule
                {
                    name = "constantParameterTypes",
                    description = "检查关键参数类型必须为Constant",
                    expression = "main.startDelay.mode == 0 && main.startLifetime.mode == 0 && main.startSpeed.mode == 0 && main.startSize.mode == 0 && main.startRotation.mode == 0",
                    severity = ValidationSeverity.Error,
                    errorMessage = "Start Delay, Start Lifetime, Start Speed, Start Size, Start Rotation的类型必须为Constant"
                },
                new ExpressionRule
                {
                    name = "zeroValueParameters",
                    description = "检查Flip Rotation和Gravity Modifier必须为0",
                    expression = "main.flipRotation == 0 && main.gravityModifier == 0",
                    severity = ValidationSeverity.Error,
                    errorMessage = "Flip Rotation和Gravity Modifier的值必须为0"
                },
                new ExpressionRule
                {
                    name = "startColorType",
                    description = "检查Start Color类型必须为Color",
                    expression = "main.startColor.mode == 0",
                    severity = ValidationSeverity.Error,
                    errorMessage = "Start Color的类型必须为Color"
                },
                new ExpressionRule
                {
                    name = "simulationSettings",
                    description = "检查仿真设置参数",
                    expression = "main.simulationSpace == 0 && main.simulationSpeed == 1 && main.useUnscaledTime == false && main.scalingMode == 1 && main.useRigidbodyForVelocity == false && main.cullingMode == 0",
                    severity = ValidationSeverity.Error,
                    errorMessage = "仿真设置不符合要求: Simulation Space=Local, Simulation Speed=1, Delta Time=Scaled, Scaling Mode=Hierarchy, Ring Buffer Mode=Disabled, Culling Mode=Automatic"
                },
                new ExpressionRule
                {
                    name = "emissionSettings",
                    description = "检查Emission组件设置",
                    expression = "emission.rateOverTime > 0 && emission.rateOverDistance == 0 && emission.burstCount == 0",
                    severity = ValidationSeverity.Error,
                    errorMessage = "Emission设置不符合要求: Rate over Time>0, Rate over Distance=0, Bursts列表为空"
                }
            };

            return config;
        }
    }
}
