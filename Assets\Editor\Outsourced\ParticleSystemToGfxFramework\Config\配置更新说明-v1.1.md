# 配置文件更新说明 - v1.1

## 📋 更新概述

**版本**：1.0 → 1.1  
**更新时间**：2025-06-08  
**更新内容**：增加了13个新的精细化验证规则

## 🆕 新增验证规则

### **规则4：Prewarm检查**
- **规则名称**：`prewarmDisabled`
- **表达式**：`main.prewarm == false`
- **要求**：Prewarm必须为关闭状态
- **错误信息**：Prewarm必须为关闭状态

### **规则5：参数类型检查**
- **规则名称**：`constantParameterTypes`
- **表达式**：`main.startDelay.mode == 0 && main.startLifetime.mode == 0 && main.startSpeed.mode == 0 && main.startSize.mode == 0 && main.startRotation.mode == 0`
- **要求**：Start Delay、Start Lifetime、Start Speed、Start Size、Start Rotation的类型必须为Constant (mode=0)
- **错误信息**：Start Delay, Start Lifetime, Start Speed, Start Size, Start Rotation的类型必须为Constant

### **规则6：零值参数检查**
- **规则名称**：`zeroValueParameters`
- **表达式**：`main.flipRotation == 0 && main.gravityModifier == 0`
- **要求**：Flip Rotation和Gravity Modifier的值必须为0
- **错误信息**：Flip Rotation和Gravity Modifier的值必须为0

### **规则7：颜色类型检查**
- **规则名称**：`startColorType`
- **表达式**：`main.startColor.mode == 0`
- **要求**：Start Color的类型必须为Color (mode=0)
- **错误信息**：Start Color的类型必须为Color

### **规则8：仿真设置检查**
- **规则名称**：`simulationSettings`
- **表达式**：`main.simulationSpace == 0 && main.simulationSpeed == 1 && main.useUnscaledTime == false && main.scalingMode == 1 && main.useRigidbodyForVelocity == false && main.cullingMode == 0`
- **要求**：
  - Simulation Space = Local (0)
  - Simulation Speed = 1
  - Delta Time = Scaled (useUnscaledTime = false)
  - Scaling Mode = Hierarchy (1)
  - Ring Buffer Mode = Disabled (useRigidbodyForVelocity = false)
  - Culling Mode = Automatic (0)
- **错误信息**：仿真设置不符合要求: Simulation Space=Local, Simulation Speed=1, Delta Time=Scaled, Scaling Mode=Hierarchy, Ring Buffer Mode=Disabled, Culling Mode=Automatic

### **规则9：发射设置检查**
- **规则名称**：`emissionSettings`
- **表达式**：`emission.rateOverTime > 0 && emission.rateOverDistance == 0 && emission.burstCount == 0`
- **要求**：
  - Rate over Time > 0
  - Rate over Distance = 0
  - Bursts列表为空 (burstCount = 0)
- **错误信息**：Emission设置不符合要求: Rate over Time>0, Rate over Distance=0, Bursts列表为空

## 📊 完整规则列表

### **原有规则 (v1.0)**
1. **maxParticles** - Max Particles = 1
2. **renderModeAndMesh** - Render Mode = Mesh且Mesh不为空
3. **forbiddenModules** - 16个禁用模块检查

### **新增规则 (v1.1)**
4. **prewarmDisabled** - Prewarm关闭检查
5. **constantParameterTypes** - 参数类型为Constant检查
6. **zeroValueParameters** - 零值参数检查
7. **startColorType** - Start Color类型检查
8. **simulationSettings** - 仿真设置参数检查
9. **emissionSettings** - Emission组件设置检查

**总计**：9个验证规则

## 🎯 规则映射表

| 用户要求 | 规则名称 | 表达式关键部分 |
|---------|---------|---------------|
| 1. Prewarm必须关闭 | prewarmDisabled | `main.prewarm == false` |
| 2. 参数类型为Constant | constantParameterTypes | `*.mode == 0` |
| 3. Flip Rotation = 0 | zeroValueParameters | `main.flipRotation == 0` |
| 4. Gravity Modifier = 0 | zeroValueParameters | `main.gravityModifier == 0` |
| 5. Start Color类型为Color | startColorType | `main.startColor.mode == 0` |
| 6. Simulation Space = Local | simulationSettings | `main.simulationSpace == 0` |
| 7. Simulation Speed = 1 | simulationSettings | `main.simulationSpeed == 1` |
| 8. Delta Time = Scaled | simulationSettings | `main.useUnscaledTime == false` |
| 9. Scaling Mode = Hierarchy | simulationSettings | `main.scalingMode == 1` |
| 10. Ring Buffer Mode = Disabled | simulationSettings | `main.useRigidbodyForVelocity == false` |
| 11. Culling Mode = Automatic | simulationSettings | `main.cullingMode == 0` |
| 12. Rate over Time > 0 | emissionSettings | `emission.rateOverTime > 0` |
| 13. Rate over Distance = 0 | emissionSettings | `emission.rateOverDistance == 0` |
| 14. Bursts列表为空 | emissionSettings | `emission.burstCount == 0` |

## 🔧 使用说明

1. **配置文件位置**：`Assets/Editor/Outsourced/ParticleSystemToGfxFramework/Config/DefaultConversionRules.asset`
2. **代码同步**：`ValidationConfig.cs`中的`CreateDefault()`方法已同步更新
3. **版本标识**：配置文件版本已更新为1.1
4. **向后兼容**：新规则不影响现有验证逻辑

## ✅ 验证状态

- ✅ 配置文件格式正确
- ✅ Unity编译通过，无错误
- ✅ 代码和配置文件同步
- ✅ 所有13个新要求已实现

## 📝 注意事项

1. **参数名称**：表达式中的参数名称基于Unity ParticleSystem API
2. **数值映射**：枚举值使用数字表示（如mode=0表示Constant）
3. **逻辑运算**：使用`&&`连接多个条件，要求全部满足
4. **错误级别**：所有新规则都设置为Error级别，验证失败会阻止转换
