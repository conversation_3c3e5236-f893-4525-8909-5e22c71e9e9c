%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f52912c59943cc84b8929ed65e66cb16, type: 3}
  m_Name: DefaultConversionRules
  m_EditorClassIdentifier: 
  version: 1.2
  description: "\u9ED8\u8BA4\u7C92\u5B50\u7CFB\u7EDF\u8F6C\u6362\u9A8C\u8BC1\u89C4\u5219
    - \u4F18\u5316\u4E86Emission\u7EC4\u4EF6\u9A8C\u8BC1\u89C4\u5219"
  rules:
  - name: maxParticles
    description: "\u68C0\u67E5\u7C92\u5B50\u6700\u5927\u6570\u91CF\u5FC5\u987B\u4E3A1"
    expression: main.maxParticles == 1
    severity: 2
    errorMessage: "Max Particles\u5FC5\u987B\u7B49\u4E8E1"
  - name: renderModeAndMesh
    description: "\u68C0\u67E5\u6E32\u67D3\u6A21\u5F0F\u5FC5\u987B\u4E3AMesh\u4E14Mesh\u4E0D\u4E3A\u7A7A"
    expression: renderer.renderMode == 4 && renderer.mesh != null
    severity: 2
    errorMessage: "\u5FC5\u987B\u4F7F\u7528Mesh\u6E32\u67D3\u6A21\u5F0F\u4E14Mesh\u4E0D\u4E3A\u7A7A"
  - name: forbiddenModules
    description: "\u68C0\u67E5\u7981\u7528\u6A21\u5757\u672A\u542F\u7528"
    expression: '!shape.enabled && !velocityOverLifetime.enabled && !limitVelocityOverLifetime.enabled
      && !inheritVelocity.enabled && !forceOverLifetime.enabled && !colorBySpeed.enabled
      && !sizeBySpeed.enabled && !rotationBySpeed.enabled && !externalForces.enabled
      && !noise.enabled && !collision.enabled && !trigger.enabled && !subEmitters.enabled
      && !textureSheetAnimation.enabled && !lights.enabled && !trails.enabled'
    severity: 2
    errorMessage: "\u4E0D\u80FD\u542F\u7528\u4EE5\u4E0B\u7981\u7528\u6A21\u5757: Shape,
      Velocity over Lifetime, Limit Velocity over Lifetime, Inherit Velocity, Force
      over Lifetime, Color by Speed, Size by Speed, Rotation by Speed, External Forces,
      Noise, Collision, Triggers, Sub Emitters, Texture Sheet Animation, Lights, Trails"
  - name: prewarmDisabled
    description: "\u68C0\u67E5Prewarm\u5FC5\u987B\u4E3A\u5173\u95ED"
    expression: main.prewarm == false
    severity: 2
    errorMessage: "Prewarm\u5FC5\u987B\u4E3A\u5173\u95ED\u72B6\u6001"
  - name: constantParameterTypes
    description: "\u68C0\u67E5\u5173\u952E\u53C2\u6570\u7C7B\u578B\u5FC5\u987B\u4E3AConstant"
    expression: main.startDelay.mode == 0 && main.startLifetime.mode == 0 && main.startSpeed.mode
      == 0 && main.startSize.mode == 0 && main.startRotation.mode == 0
    severity: 2
    errorMessage: "Start Delay, Start Lifetime, Start Speed, Start Size, Start Rotation\u7684\u7C7B\u578B\u5FC5\u987B\u4E3AConstant"
  - name: zeroValueParameters
    description: "\u68C0\u67E5Flip Rotation\u548CGravity Modifier\u5FC5\u987B\u4E3A0"
    expression: main.flipRotation == 0 && main.gravityModifier == 0
    severity: 2
    errorMessage: "Flip Rotation\u548CGravity Modifier\u7684\u503C\u5FC5\u987B\u4E3A0"
  - name: startColorType
    description: "\u68C0\u67E5Start Color\u7C7B\u578B\u5FC5\u987B\u4E3AColor"
    expression: main.startColor.mode == 0
    severity: 2
    errorMessage: "Start Color\u7684\u7C7B\u578B\u5FC5\u987B\u4E3AColor"
  - name: simulationSettings
    description: "\u68C0\u67E5\u4EFF\u771F\u8BBE\u7F6E\u53C2\u6570"
    expression: main.simulationSpace == 0 && main.simulationSpeed == 1 && main.useUnscaledTime
      == false && main.scalingMode == 1 && main.useRigidbodyForVelocity == false &&
      main.cullingMode == 0
    severity: 2
    errorMessage: "\u4EFF\u771F\u8BBE\u7F6E\u4E0D\u7B26\u5408\u8981\u6C42: Simulation
      Space=Local, Simulation Speed=1, Delta Time=Scaled, Scaling Mode=Hierarchy,
      Ring Buffer Mode=Disabled, Culling Mode=Automatic"
  - name: emissionSettings
    description: "\u68C0\u67E5Emission\u7EC4\u4EF6\u8BBE\u7F6E"
    expression: emission.rateOverDistance == 0 && emission.burstCount == 1 && emission.GetBurst(0).time
      == 0 && emission.GetBurst(0).count.constant == 1 && emission.GetBurst(0).cycleCount
      == 1 && emission.GetBurst(0).probability == 1
    severity: 2
    errorMessage: "Emission\u8BBE\u7F6E\u4E0D\u7B26\u5408\u8981\u6C42: Rate over Distance=0,
      \u4E14\u5FC5\u987B\u6709\u4E14\u4EC5\u6709\u4E00\u4E2ABurst\uFF0CTime=0, Count=1,
      Cycles=1, Probability=1"
