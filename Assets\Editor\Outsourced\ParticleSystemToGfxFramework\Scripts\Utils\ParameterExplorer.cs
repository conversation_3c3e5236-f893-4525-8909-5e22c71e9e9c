using System;
using System.Collections.Generic;
using System.Reflection;
using UnityEditor;
using UnityEngine;

namespace ParticleSystemToGfxFramework.Utils
{
    /// <summary>
    /// 粒子系统参数探测器窗口
    /// 帮助配表人员了解ParticleSystem的所有可用属性名
    /// </summary>
    public class ParameterExplorer : EditorWindow
    {
        #region 窗口管理
        
        [MenuItem("TATools/粒子系统参数探测器")]
        public static void ShowWindow()
        {
            var window = GetWindow<ParameterExplorer>("粒子系统参数探测器");
            window.minSize = new Vector2(600, 400);
            window.Show();
        }
        
        #endregion
        
        #region 私有字段
        
        private ParticleSystem _currentParticleSystem;
        private Vector2 _scrollPosition;
        private List<ParameterInfo> _parameters = new List<ParameterInfo>();
        private List<ParameterInfo> _allParameters = new List<ParameterInfo>();
        private string _searchFilter = "";
        private bool _showOnlyEnabled = false;
        
        #endregion
        
        #region 数据结构
        
        /// <summary>
        /// 参数信息
        /// </summary>
        private class ParameterInfo
        {
            public string Path { get; set; }
            public string Type { get; set; }
            public string Value { get; set; }
            public string Description { get; set; }
            public bool IsEnabled { get; set; }
        }
        
        #endregion
        
        #region Unity生命周期
        
        private void OnGUI()
        {
            DrawHeader();
            DrawControls();
            DrawParameterList();
        }
        
        private void OnSelectionChange()
        {
            RefreshCurrentParticleSystem();
            Repaint();
        }
        
        #endregion
        
        #region UI绘制
        
        private void DrawHeader()
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("粒子系统参数探测器", EditorStyles.boldLabel);
            EditorGUILayout.LabelField("选择Hierarchy中包含ParticleSystem的GameObject来查看其属性", EditorStyles.helpBox);
            EditorGUILayout.Space();
        }
        
        private void DrawControls()
        {
            EditorGUILayout.BeginHorizontal();
            
            // 当前选中的粒子系统
            EditorGUILayout.LabelField("当前粒子系统:", GUILayout.Width(100));
            if (_currentParticleSystem != null)
            {
                EditorGUILayout.LabelField(_currentParticleSystem.name, EditorStyles.textField);
            }
            else
            {
                EditorGUILayout.LabelField("未选中", EditorStyles.helpBox);
            }
            
            EditorGUILayout.EndHorizontal();
            // 添加垂直间距
            EditorGUILayout.Space();

            // 搜索过滤 - 第一行
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("搜索:", GUILayout.Width(50));
            string newSearchFilter = EditorGUILayout.TextField(_searchFilter);
            if (newSearchFilter != _searchFilter)
            {
                _searchFilter = newSearchFilter;
                FilterParameters();
            }
            EditorGUILayout.EndHorizontal();

            // 添加垂直间距
            EditorGUILayout.Space();

            // 过滤选项和刷新按钮 - 第二行
            EditorGUILayout.BeginHorizontal();

            // 只显示非默认值
            bool newShowOnlyEnabled = EditorGUILayout.Toggle("只显示非默认值", _showOnlyEnabled);
            if (newShowOnlyEnabled != _showOnlyEnabled)
            {
                _showOnlyEnabled = newShowOnlyEnabled;
                FilterParameters();
            }

            // 填充空间，让刷新按钮靠右
            GUILayout.FlexibleSpace();

            // 刷新按钮
            if (GUILayout.Button("刷新", GUILayout.Width(80)))
            {
                RefreshParameters();
            }

            EditorGUILayout.EndHorizontal();
            EditorGUILayout.Space();
        }
        
        private void DrawParameterList()
        {
            if (_currentParticleSystem == null)
            {
                EditorGUILayout.HelpBox("请在Hierarchy中选择包含ParticleSystem组件的GameObject", MessageType.Info);
                return;
            }
            
            if (_parameters.Count == 0)
            {
                EditorGUILayout.HelpBox("没有找到参数或正在加载中...", MessageType.Warning);
                return;
            }
            
            // 表头
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("属性路径", EditorStyles.boldLabel, GUILayout.Width(200));
            EditorGUILayout.LabelField("类型", EditorStyles.boldLabel, GUILayout.Width(80));
            EditorGUILayout.LabelField("当前值", EditorStyles.boldLabel, GUILayout.Width(120));
            EditorGUILayout.LabelField("说明", EditorStyles.boldLabel);
            EditorGUILayout.LabelField("操作", EditorStyles.boldLabel, GUILayout.Width(60));
            EditorGUILayout.EndHorizontal();
            
            // 分隔线
            EditorGUILayout.Space();
            
            // 参数列表
            _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition);
            
            foreach (var param in _parameters)
            {
                DrawParameterRow(param);
            }
            
            EditorGUILayout.EndScrollView();
        }
        
        private void DrawParameterRow(ParameterInfo param)
        {
            EditorGUILayout.BeginHorizontal();
            
            // 属性路径
            EditorGUILayout.SelectableLabel(param.Path, GUILayout.Width(200), GUILayout.Height(18));
            
            // 类型
            EditorGUILayout.LabelField(param.Type, GUILayout.Width(80));
            
            // 当前值
            EditorGUILayout.SelectableLabel(param.Value, GUILayout.Width(120), GUILayout.Height(18));
            
            // 说明
            EditorGUILayout.LabelField(param.Description);
            
            // 复制按钮
            if (GUILayout.Button("复制", GUILayout.Width(60)))
            {
                EditorGUIUtility.systemCopyBuffer = param.Path;
                ConversionLogger.LogInfo($"已复制属性路径: {param.Path}");
            }
            
            EditorGUILayout.EndHorizontal();
        }
        
        #endregion

        #region 核心逻辑

        /// <summary>
        /// 刷新当前选中的粒子系统
        /// </summary>
        private void RefreshCurrentParticleSystem()
        {
            ParticleSystem newParticleSystem = null;

            if (Selection.activeGameObject != null)
            {
                newParticleSystem = Selection.activeGameObject.GetComponent<ParticleSystem>();
            }

            if (newParticleSystem != _currentParticleSystem)
            {
                _currentParticleSystem = newParticleSystem;
                RefreshParameters();
            }
        }

        /// <summary>
        /// 刷新参数列表
        /// </summary>
        private void RefreshParameters()
        {
            _allParameters.Clear();
            _parameters.Clear();

            if (_currentParticleSystem == null)
                return;

            try
            {
                // 获取所有模块的参数
                ExtractMainModuleParameters();
                ExtractEmissionParameters();
                ExtractShapeParameters();
                ExtractVelocityOverLifetimeParameters();
                ExtractColorOverLifetimeParameters();
                ExtractSizeOverLifetimeParameters();
                ExtractRotationOverLifetimeParameters();
                ExtractRendererParameters();
                ExtractOtherModulesParameters();

                // 复制到全量列表
                _allParameters.AddRange(_parameters);

                FilterParameters();
                ConversionLogger.LogInfo($"成功提取 {_allParameters.Count} 个参数，显示 {_parameters.Count} 个");
            }
            catch (Exception ex)
            {
                ConversionLogger.LogError($"提取参数时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 提取Main模块参数
        /// </summary>
        private void ExtractMainModuleParameters()
        {
            var main = _currentParticleSystem.main;

            AddParameter("main.duration", main.duration.ToString(), "持续时间", true);
            AddParameter("main.loop", main.loop.ToString(), "是否循环", true);
            AddParameter("main.prewarm", main.prewarm.ToString(), "预热", true);

            // 开始延迟相关参数
            AddParameter("main.startDelay", GetMinMaxCurveValue(main.startDelay), "开始延迟", true);
            AddParameter("main.startDelay.mode", ((int)main.startDelay.mode).ToString(), $"开始延迟类型 ({main.startDelay.mode})", true);

            // 粒子生命周期相关参数
            AddParameter("main.startLifetime", GetMinMaxCurveValue(main.startLifetime), "粒子生命周期", true);
            AddParameter("main.startLifetime.mode", ((int)main.startLifetime.mode).ToString(), $"生命周期类型 ({main.startLifetime.mode})", true);

            // 初始速度相关参数
            AddParameter("main.startSpeed", GetMinMaxCurveValue(main.startSpeed), "初始速度", true);
            AddParameter("main.startSpeed.mode", ((int)main.startSpeed.mode).ToString(), $"初始速度类型 ({main.startSpeed.mode})", true);

            // 初始大小相关参数
            AddParameter("main.startSize", GetMinMaxCurveValue(main.startSize), "初始大小", true);
            AddParameter("main.startSize.mode", ((int)main.startSize.mode).ToString(), $"初始大小类型 ({main.startSize.mode})", true);

            // 初始旋转相关参数
            AddParameter("main.startRotation", GetMinMaxCurveValue(main.startRotation), "初始旋转", true);
            AddParameter("main.startRotation.mode", ((int)main.startRotation.mode).ToString(), $"初始旋转类型 ({main.startRotation.mode})", true);

            // 初始颜色相关参数
            AddParameter("main.startColor", main.startColor.ToString(), "初始颜色", true);
            AddParameter("main.startColor.mode", ((int)main.startColor.mode).ToString(), $"初始颜色类型 ({main.startColor.mode})", true);

            // 重力和翻转参数
            AddParameter("main.gravityModifier", GetMinMaxCurveValue(main.gravityModifier), "重力修正", true);
            AddParameter("main.flipRotation", main.flipRotation.ToString(), "翻转旋转", true);

            // 仿真设置参数
            AddParameter("main.simulationSpace", main.simulationSpace.ToString(), "模拟空间", true);
            AddParameter("main.simulationSpeed", main.simulationSpeed.ToString(), "仿真速度", true);
            AddParameter("main.useUnscaledTime", main.useUnscaledTime.ToString(), "使用未缩放时间 (Delta Time)", true);
            AddParameter("main.scalingMode", main.scalingMode.ToString(), "缩放模式", true);
            AddParameter("main.ringBufferMode", main.ringBufferMode.ToString(), "环形缓冲模式 (Ring Buffer Mode)", true);
            AddParameter("main.cullingMode", main.cullingMode.ToString(), "剔除模式", true);

            AddParameter("main.maxParticles", main.maxParticles.ToString(), "最大粒子数", true);
        }

        /// <summary>
        /// 提取Emission模块参数
        /// </summary>
        private void ExtractEmissionParameters()
        {
            var emission = _currentParticleSystem.emission;
            bool enabled = emission.enabled;

            AddParameter("emission.enabled", enabled.ToString(), "发射模块启用状态", true);
            AddParameter("emission.rateOverTime", GetMinMaxCurveValue(emission.rateOverTime), "每秒发射数量", enabled);
            AddParameter("emission.rateOverDistance", GetMinMaxCurveValue(emission.rateOverDistance), "每距离发射数量", enabled);

            // Burst相关参数
            AddParameter("emission.burstCount", emission.burstCount.ToString(), "Burst数量", enabled);

            // 如果有Burst，显示第一个Burst的详细信息
            if (emission.burstCount > 0)
            {
                try
                {
                    var burst = emission.GetBurst(0);
                    AddParameter("emission.GetBurst(0).time", burst.time.ToString("F2"), "Burst[0] 时间", enabled);
                    AddParameter("emission.GetBurst(0).count.constant", burst.count.constant.ToString("F2"), "Burst[0] 数量", enabled);
                    AddParameter("emission.GetBurst(0).cycleCount", burst.cycleCount.ToString(), "Burst[0] 循环次数", enabled);
                    AddParameter("emission.GetBurst(0).probability", burst.probability.ToString("F2"), "Burst[0] 概率", enabled);
                }
                catch (System.Exception ex)
                {
                    AddParameter("emission.GetBurst(0)", $"Error: {ex.Message}", "Burst[0] 访问错误", enabled);
                }
            }
            else
            {
                AddParameter("emission.GetBurst(0)", "No bursts available", "Burst[0] 不可用", enabled);
            }
        }

        /// <summary>
        /// 提取Shape模块参数
        /// </summary>
        private void ExtractShapeParameters()
        {
            var shape = _currentParticleSystem.shape;
            bool enabled = shape.enabled;

            AddParameter("shape.enabled", enabled.ToString(), "形状模块启用状态", true);
            AddParameter("shape.shapeType", shape.shapeType.ToString(), "形状类型", enabled);
            AddParameter("shape.angle", shape.angle.ToString(), "角度", enabled);
            AddParameter("shape.radius", shape.radius.ToString(), "半径", enabled);
        }

        /// <summary>
        /// 提取VelocityOverLifetime模块参数
        /// </summary>
        private void ExtractVelocityOverLifetimeParameters()
        {
            var velocityOverLifetime = _currentParticleSystem.velocityOverLifetime;
            bool enabled = velocityOverLifetime.enabled;

            AddParameter("velocityOverLifetime.enabled", enabled.ToString(), "生命周期速度模块启用状态", true);
            AddParameter("velocityOverLifetime.space", velocityOverLifetime.space.ToString(), "坐标空间", enabled);
        }

        /// <summary>
        /// 提取ColorOverLifetime模块参数
        /// </summary>
        private void ExtractColorOverLifetimeParameters()
        {
            var colorOverLifetime = _currentParticleSystem.colorOverLifetime;
            bool enabled = colorOverLifetime.enabled;

            AddParameter("colorOverLifetime.enabled", enabled.ToString(), "生命周期颜色模块启用状态", true);
            AddParameter("colorOverLifetime.color", colorOverLifetime.color.ToString(), "颜色渐变", enabled);
        }

        /// <summary>
        /// 提取SizeOverLifetime模块参数
        /// </summary>
        private void ExtractSizeOverLifetimeParameters()
        {
            var sizeOverLifetime = _currentParticleSystem.sizeOverLifetime;
            bool enabled = sizeOverLifetime.enabled;

            AddParameter("sizeOverLifetime.enabled", enabled.ToString(), "生命周期大小模块启用状态", true);
            AddParameter("sizeOverLifetime.separateAxes", sizeOverLifetime.separateAxes.ToString(), "分轴缩放", enabled);
            AddParameter("sizeOverLifetime.size", GetMinMaxCurveValue(sizeOverLifetime.size), "大小曲线", enabled);
        }

        /// <summary>
        /// 提取RotationOverLifetime模块参数
        /// </summary>
        private void ExtractRotationOverLifetimeParameters()
        {
            var rotationOverLifetime = _currentParticleSystem.rotationOverLifetime;
            bool enabled = rotationOverLifetime.enabled;

            AddParameter("rotationOverLifetime.enabled", enabled.ToString(), "生命周期旋转模块启用状态", true);
            AddParameter("rotationOverLifetime.separateAxes", rotationOverLifetime.separateAxes.ToString(), "分轴旋转", enabled);
            AddParameter("rotationOverLifetime.z", GetMinMaxCurveValue(rotationOverLifetime.z), "Z轴旋转", enabled);
        }

        /// <summary>
        /// 提取Renderer参数
        /// </summary>
        private void ExtractRendererParameters()
        {
            var renderer = _currentParticleSystem.GetComponent<ParticleSystemRenderer>();
            if (renderer == null) return;

            AddParameter("renderer.renderMode", ((int)renderer.renderMode).ToString(), $"渲染模式 ({renderer.renderMode})", true);
            AddParameter("renderer.mesh", renderer.mesh?.name ?? "null", "网格", true);
            AddParameter("renderer.material", renderer.material?.name ?? "null", "材质", true);
            AddParameter("renderer.sortingOrder", renderer.sortingOrder.ToString(), "排序顺序", true);
        }

        /// <summary>
        /// 提取其他模块参数
        /// </summary>
        private void ExtractOtherModulesParameters()
        {
            // 限制速度模块
            var limitVelocityOverLifetime = _currentParticleSystem.limitVelocityOverLifetime;
            AddParameter("limitVelocityOverLifetime.enabled", limitVelocityOverLifetime.enabled.ToString(), "限制速度模块启用状态", true);

            // 继承速度模块
            var inheritVelocity = _currentParticleSystem.inheritVelocity;
            AddParameter("inheritVelocity.enabled", inheritVelocity.enabled.ToString(), "继承速度模块启用状态", true);

            // 力场模块
            var forceOverLifetime = _currentParticleSystem.forceOverLifetime;
            AddParameter("forceOverLifetime.enabled", forceOverLifetime.enabled.ToString(), "力场模块启用状态", true);

            // 速度颜色模块
            var colorBySpeed = _currentParticleSystem.colorBySpeed;
            AddParameter("colorBySpeed.enabled", colorBySpeed.enabled.ToString(), "速度颜色模块启用状态", true);

            // 速度大小模块
            var sizeBySpeed = _currentParticleSystem.sizeBySpeed;
            AddParameter("sizeBySpeed.enabled", sizeBySpeed.enabled.ToString(), "速度大小模块启用状态", true);

            // 速度旋转模块
            var rotationBySpeed = _currentParticleSystem.rotationBySpeed;
            AddParameter("rotationBySpeed.enabled", rotationBySpeed.enabled.ToString(), "速度旋转模块启用状态", true);

            // 外力模块
            var externalForces = _currentParticleSystem.externalForces;
            AddParameter("externalForces.enabled", externalForces.enabled.ToString(), "外力模块启用状态", true);

            // 噪声模块
            var noise = _currentParticleSystem.noise;
            AddParameter("noise.enabled", noise.enabled.ToString(), "噪声模块启用状态", true);

            // 碰撞模块
            var collision = _currentParticleSystem.collision;
            AddParameter("collision.enabled", collision.enabled.ToString(), "碰撞模块启用状态", true);

            // 触发器模块
            var trigger = _currentParticleSystem.trigger;
            AddParameter("trigger.enabled", trigger.enabled.ToString(), "触发器模块启用状态", true);

            // 子发射器模块
            var subEmitters = _currentParticleSystem.subEmitters;
            AddParameter("subEmitters.enabled", subEmitters.enabled.ToString(), "子发射器模块启用状态", true);

            // 纹理表动画模块
            var textureSheetAnimation = _currentParticleSystem.textureSheetAnimation;
            AddParameter("textureSheetAnimation.enabled", textureSheetAnimation.enabled.ToString(), "纹理表动画模块启用状态", true);

            // 光照模块
            var lights = _currentParticleSystem.lights;
            AddParameter("lights.enabled", lights.enabled.ToString(), "光照模块启用状态", true);

            // 拖尾模块
            var trails = _currentParticleSystem.trails;
            AddParameter("trails.enabled", trails.enabled.ToString(), "拖尾模块启用状态", true);
        }

        /// <summary>
        /// 添加参数信息
        /// </summary>
        private void AddParameter(string path, string value, string description, bool isEnabled)
        {
            _parameters.Add(new ParameterInfo
            {
                Path = path,
                Type = GetValueType(value),
                Value = value,
                Description = description,
                IsEnabled = isEnabled
            });
        }

        /// <summary>
        /// 获取MinMaxCurve的值
        /// </summary>
        private string GetMinMaxCurveValue(ParticleSystem.MinMaxCurve curve)
        {
            switch (curve.mode)
            {
                case ParticleSystemCurveMode.Constant:
                    return curve.constant.ToString("F2");
                case ParticleSystemCurveMode.TwoConstants:
                    return $"{curve.constantMin:F2} - {curve.constantMax:F2}";
                case ParticleSystemCurveMode.Curve:
                    return "Curve";
                case ParticleSystemCurveMode.TwoCurves:
                    return "TwoCurves";
                default:
                    return curve.constant.ToString("F2");
            }
        }

        /// <summary>
        /// 获取值的类型
        /// </summary>
        private string GetValueType(string value)
        {
            if (bool.TryParse(value, out _)) return "bool";
            if (int.TryParse(value, out _)) return "int";
            if (float.TryParse(value, out _)) return "float";
            if (value.Contains("-")) return "range";
            if (value == "Curve" || value == "TwoCurves") return "curve";
            return "string";
        }

        /// <summary>
        /// 过滤参数列表
        /// </summary>
        private void FilterParameters()
        {
            _parameters.Clear();

            foreach (var param in _allParameters)
            {
                // 搜索过滤
                if (!string.IsNullOrEmpty(_searchFilter))
                {
                    bool matchesSearch = param.Path.IndexOf(_searchFilter, StringComparison.OrdinalIgnoreCase) >= 0 ||
                                       param.Description.IndexOf(_searchFilter, StringComparison.OrdinalIgnoreCase) >= 0 ||
                                       param.Value.IndexOf(_searchFilter, StringComparison.OrdinalIgnoreCase) >= 0;

                    if (!matchesSearch)
                        continue;
                }

                // 只显示启用状态过滤（显示非默认值）
                if (_showOnlyEnabled)
                {
                    if (!IsNonDefaultValue(param))
                        continue;
                }

                _parameters.Add(param);
            }
        }

        /// <summary>
        /// 判断参数值是否为非默认值
        /// </summary>
        private bool IsNonDefaultValue(ParameterInfo param)
        {
            // 根据属性路径判断是否为非默认值
            switch (param.Path)
            {
                // Main模块默认值
                case "main.duration":
                    return param.Value != "5.00";
                case "main.loop":
                    return param.Value != "True";
                case "main.prewarm":
                    return param.Value != "False";
                case "main.startDelay":
                    return param.Value != "0.00";
                case "main.startLifetime":
                    return param.Value != "5.00";
                case "main.startSpeed":
                    return param.Value != "5.00";
                case "main.startSize":
                    return param.Value != "1.00";
                case "main.startRotation":
                    return param.Value != "0.00";
                case "main.gravityModifier":
                    return param.Value != "0.00";
                case "main.simulationSpace":
                    return param.Value != "Local";
                case "main.maxParticles":
                    return param.Value != "1000";

                // 新增参数的默认值
                case "main.startDelay.mode":
                case "main.startLifetime.mode":
                case "main.startSpeed.mode":
                case "main.startSize.mode":
                case "main.startRotation.mode":
                case "main.startColor.mode":
                    return param.Value != "0"; // Constant模式
                case "main.flipRotation":
                    return param.Value != "0";
                case "main.simulationSpeed":
                    return param.Value != "1";
                case "main.useUnscaledTime":
                    return param.Value != "False"; // Scaled时间
                case "main.scalingMode":
                    return param.Value != "Hierarchy" && param.Value != "0"; // Hierarchy对应值为0
                case "main.ringBufferMode":
                    return param.Value != "Disabled"; // Ring Buffer Disabled
                case "main.cullingMode":
                    return param.Value != "Automatic";

                // Emission模块默认值
                case "emission.enabled":
                    return param.Value != "True";
                case "emission.rateOverTime":
                    return param.Value != "10.00";
                case "emission.rateOverDistance":
                    return param.Value != "0.00";
                case "emission.burstCount":
                    return param.Value != "0";
                case "emission.GetBurst(0).time":
                    return param.Value != "0.00";
                case "emission.GetBurst(0).count.constant":
                    return param.Value != "1.00";
                case "emission.GetBurst(0).cycleCount":
                    return param.Value != "1";
                case "emission.GetBurst(0).probability":
                    return param.Value != "1.00";

                // 所有模块的enabled状态，默认为False的显示True值
                case var path when path.EndsWith(".enabled") && !path.StartsWith("emission"):
                    return param.Value == "True";

                // Renderer默认值
                case "renderer.renderMode":
                    return param.Value != "0"; // Billboard
                case "renderer.mesh":
                    return param.Value != "null";
                case "renderer.material":
                    return param.Value != "null";
                case "renderer.sortingOrder":
                    return param.Value != "0";

                // 其他属性，如果值不是常见的默认值就显示
                default:
                    return !IsCommonDefaultValue(param.Value);
            }
        }

        /// <summary>
        /// 判断是否为常见的默认值
        /// </summary>
        private bool IsCommonDefaultValue(string value)
        {
            return value == "0.00" ||
                   value == "0" ||
                   value == "False" ||
                   value == "null" ||
                   value == "Local" ||
                   value == "1.00";
        }

        #endregion
    }
}
