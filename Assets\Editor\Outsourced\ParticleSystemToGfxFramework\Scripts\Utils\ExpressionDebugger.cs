using UnityEngine;
using UnityEditor;

namespace ParticleSystemToGfxFramework.Utils
{
    /// <summary>
    /// 表达式调试器 - 用于测试和调试表达式执行
    /// </summary>
    public class ExpressionDebugger : EditorWindow
    {
        private ParticleSystem _targetParticleSystem;
        private string _testExpression = "main.startDelay.mode == 0";
        private Vector2 _scrollPosition;
        
        [MenuItem("TATools/表达式调试器")]
        public static void ShowWindow()
        {
            GetWindow<ExpressionDebugger>("表达式调试器");
        }
        
        private void OnGUI()
        {
            GUILayout.Label("表达式调试器", EditorStyles.boldLabel);
            
            EditorGUILayout.Space();
            
            // 选择粒子系统
            _targetParticleSystem = (ParticleSystem)EditorGUILayout.ObjectField(
                "目标粒子系统", _targetParticleSystem, typeof(ParticleSystem), true);
            
            EditorGUILayout.Space();
            
            // 输入测试表达式
            GUILayout.Label("测试表达式:");
            _testExpression = EditorGUILayout.TextField(_testExpression);
            
            EditorGUILayout.Space();
            
            // 测试按钮
            if (GUILayout.Button("测试表达式"))
            {
                TestExpression();
            }
            
            EditorGUILayout.Space();
            
            // 预设表达式测试
            GUILayout.Label("预设表达式测试:", EditorStyles.boldLabel);
            
            if (GUILayout.Button("测试 constantParameterTypes"))
            {
                _testExpression = "main.startDelay.mode == 0 && main.startLifetime.mode == 0 && main.startSpeed.mode == 0 && main.startSize.mode == 0 && main.startRotation.mode == 0";
                TestExpression();
            }
            
            if (GUILayout.Button("测试 startColorType"))
            {
                _testExpression = "main.startColor.mode == 0";
                TestExpression();
            }
            
            if (GUILayout.Button("测试 simulationSettings"))
            {
                _testExpression = "main.simulationSpace == 0 && main.simulationSpeed == 1 && main.useUnscaledTime == false && main.scalingMode == 1 && main.ringBufferMode == 0 && main.cullingMode == 0";
                TestExpression();
            }
            
            if (GUILayout.Button("测试 emissionSettings"))
            {
                _testExpression = "emission.rateOverDistance == 0 && emission.burstCount == 1 && emission.GetBurst(0).time == 0 && emission.GetBurst(0).count.constant == 1 && emission.GetBurst(0).cycleCount == 1 && emission.GetBurst(0).probability == 1";
                TestExpression();
            }
        }
        
        private void TestExpression()
        {
            if (_targetParticleSystem == null)
            {
                Debug.LogError("[表达式调试器] 请先选择一个粒子系统");
                return;
            }
            
            if (string.IsNullOrEmpty(_testExpression))
            {
                Debug.LogError("[表达式调试器] 请输入测试表达式");
                return;
            }
            
            Debug.Log($"[表达式调试器] 开始测试表达式: {_testExpression}");
            Debug.Log($"[表达式调试器] 目标粒子系统: {_targetParticleSystem.name}");
            
            var engine = new Core.ExpressionEngine();
            bool result = engine.EvaluateExpression(_testExpression, _targetParticleSystem);
            
            Debug.Log($"[表达式调试器] 表达式执行结果: {result}");
            
            // 显示一些关键属性值
            var main = _targetParticleSystem.main;
            Debug.Log($"[表达式调试器] 关键属性值:");
            Debug.Log($"  - main.startDelay.mode = {(int)main.startDelay.mode}");
            Debug.Log($"  - main.startLifetime.mode = {(int)main.startLifetime.mode}");
            Debug.Log($"  - main.startSpeed.mode = {(int)main.startSpeed.mode}");
            Debug.Log($"  - main.startSize.mode = {(int)main.startSize.mode}");
            Debug.Log($"  - main.startRotation.mode = {(int)main.startRotation.mode}");
            Debug.Log($"  - main.startColor.mode = {(int)main.startColor.mode}");
            Debug.Log($"  - main.simulationSpace = {(int)main.simulationSpace}");

            // 检查Unity 2018.3中是否存在这些属性
            try
            {
                Debug.Log($"  - main.simulationSpeed = {main.simulationSpeed}");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"  - main.simulationSpeed 属性不存在: {ex.Message}");
            }

            try
            {
                Debug.Log($"  - main.useUnscaledTime = {main.useUnscaledTime}");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"  - main.useUnscaledTime 属性不存在: {ex.Message}");
            }

            try
            {
                Debug.Log($"  - main.scalingMode = {(int)main.scalingMode}");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"  - main.scalingMode 属性不存在: {ex.Message}");
            }

            try
            {
                Debug.Log($"  - main.ringBufferMode = {(int)main.ringBufferMode}");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"  - main.ringBufferMode 属性不存在: {ex.Message}");
            }

            try
            {
                Debug.Log($"  - main.cullingMode = {(int)main.cullingMode}");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"  - main.cullingMode 属性不存在: {ex.Message}");
            }
            
            var emission = _targetParticleSystem.emission;
            Debug.Log($"  - emission.rateOverDistance = {emission.rateOverDistance.constant}");
            Debug.Log($"  - emission.burstCount = {emission.burstCount}");
            
            if (emission.burstCount > 0)
            {
                var burst = emission.GetBurst(0);
                Debug.Log($"  - emission.GetBurst(0).time = {burst.time}");
                Debug.Log($"  - emission.GetBurst(0).count.constant = {burst.count.constant}");
                Debug.Log($"  - emission.GetBurst(0).cycleCount = {burst.cycleCount}");
                Debug.Log($"  - emission.GetBurst(0).probability = {burst.probability}");
            }
        }
    }
}
