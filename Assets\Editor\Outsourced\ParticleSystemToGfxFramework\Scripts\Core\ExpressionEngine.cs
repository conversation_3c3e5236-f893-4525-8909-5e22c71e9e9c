using System;
using System.Globalization;
using UnityEngine;
using ParticleSystemToGfxFramework.Utils;

namespace ParticleSystemToGfxFramework.Core
{
    /// <summary>
    /// 表达式执行引擎
    /// 解析和执行验证表达式，如 "main.maxParticles == 1"
    /// </summary>
    public class ExpressionEngine
    {
        /// <summary>
        /// 执行表达式并返回结果
        /// </summary>
        /// <param name="expression">表达式字符串</param>
        /// <param name="particleSystem">粒子系统对象</param>
        /// <returns>表达式执行结果</returns>
        public bool EvaluateExpression(string expression, ParticleSystem particleSystem)
        {
            if (string.IsNullOrEmpty(expression) || particleSystem == null)
                return false;
                
            try
            {
                // 预处理表达式，去除多余空格
                expression = expression.Trim();
                
                // 处理逻辑运算符
                if (expression.Contains("&&"))
                {
                    return EvaluateAndExpression(expression, particleSystem);
                }
                
                if (expression.Contains("||"))
                {
                    return EvaluateOrExpression(expression, particleSystem);
                }
                
                // 处理取反运算符
                if (expression.StartsWith("!"))
                {
                    string innerExpression = expression.Substring(1).Trim();
                    return !EvaluateExpression(innerExpression, particleSystem);
                }
                
                // 处理比较运算符
                return EvaluateComparisonExpression(expression, particleSystem);
            }
            catch (Exception ex)
            {
                ConversionLogger.LogError($"表达式执行错误: {expression}, 错误: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 处理AND逻辑运算
        /// </summary>
        private bool EvaluateAndExpression(string expression, ParticleSystem particleSystem)
        {
            string[] parts = expression.Split(new string[] { "&&" }, StringSplitOptions.RemoveEmptyEntries);
            
            foreach (string part in parts)
            {
                if (!EvaluateExpression(part.Trim(), particleSystem))
                {
                    return false;
                }
            }
            
            return true;
        }
        
        /// <summary>
        /// 处理OR逻辑运算
        /// </summary>
        private bool EvaluateOrExpression(string expression, ParticleSystem particleSystem)
        {
            string[] parts = expression.Split(new string[] { "||" }, StringSplitOptions.RemoveEmptyEntries);
            
            foreach (string part in parts)
            {
                if (EvaluateExpression(part.Trim(), particleSystem))
                {
                    return true;
                }
            }
            
            return false;
        }
        
        /// <summary>
        /// 处理比较运算表达式
        /// </summary>
        private bool EvaluateComparisonExpression(string expression, ParticleSystem particleSystem)
        {
            // 支持的比较运算符
            string[] operators = { "==", "!=", ">=", "<=", ">", "<" };
            
            foreach (string op in operators)
            {
                if (expression.Contains(op))
                {
                    string[] parts = expression.Split(new string[] { op }, StringSplitOptions.RemoveEmptyEntries);
                    if (parts.Length == 2)
                    {
                        string leftSide = parts[0].Trim();
                        string rightSide = parts[1].Trim();
                        
                        object leftValue = GetPropertyValue(leftSide, particleSystem);
                        object rightValue = ParseValue(rightSide);
                        
                        return CompareValues(leftValue, rightValue, op);
                    }
                }
            }
            
            // 如果没有比较运算符，尝试作为布尔属性处理
            object value = GetPropertyValue(expression, particleSystem);
            if (value is bool boolValue)
            {
                return boolValue;
            }
            
            return false;
        }
        
        /// <summary>
        /// 获取属性值
        /// </summary>
        private object GetPropertyValue(string propertyPath, ParticleSystem particleSystem)
        {
            string[] pathParts = propertyPath.Split('.');
            
            if (pathParts.Length < 2)
                return null;
                
            string moduleName = pathParts[0];
            string propertyName = pathParts[1];
            
            switch (moduleName.ToLower())
            {
                case "main":
                    return GetMainModuleProperty(particleSystem.main, propertyName);
                case "emission":
                    return GetEmissionModuleProperty(particleSystem.emission, propertyName);
                case "shape":
                    return GetShapeModuleProperty(particleSystem.shape, propertyName);
                case "velocityoverlifetime":
                    return GetVelocityOverLifetimeProperty(particleSystem.velocityOverLifetime, propertyName);
                case "coloroverlifetime":
                    return GetColorOverLifetimeProperty(particleSystem.colorOverLifetime, propertyName);
                case "sizeoverlifetime":
                    return GetSizeOverLifetimeProperty(particleSystem.sizeOverLifetime, propertyName);
                case "rotationoverlifetime":
                    return GetRotationOverLifetimeProperty(particleSystem.rotationOverLifetime, propertyName);
                case "renderer":
                    return GetRendererProperty(particleSystem.GetComponent<ParticleSystemRenderer>(), propertyName);
                default:
                    return GetOtherModuleProperty(particleSystem, moduleName, propertyName);
            }
        }
        
        /// <summary>
        /// 获取Main模块属性
        /// </summary>
        private object GetMainModuleProperty(ParticleSystem.MainModule main, string propertyName)
        {
            switch (propertyName.ToLower())
            {
                case "maxparticles": return main.maxParticles;
                case "duration": return main.duration;
                case "loop": return main.loop;
                case "prewarm": return main.prewarm;
                case "startdelay": return main.startDelay.constant;
                case "startlifetime": return main.startLifetime.constant;
                case "startspeed": return main.startSpeed.constant;
                case "startsize": return main.startSize.constant;
                case "startrotation": return main.startRotation.constant;
                case "gravitymodifier": return main.gravityModifier.constant;
                case "simulationspace": return (int)main.simulationSpace;
                default: return null;
            }
        }
        
        /// <summary>
        /// 获取Emission模块属性
        /// </summary>
        private object GetEmissionModuleProperty(ParticleSystem.EmissionModule emission, string propertyName)
        {
            switch (propertyName.ToLower())
            {
                case "enabled": return emission.enabled;
                case "rateovertime": return emission.rateOverTime.constant;
                case "rateoverdistance": return emission.rateOverDistance.constant;
                case "burstcount": return emission.burstCount;
                default:
                    // 处理GetBurst(0)相关属性
                    if (propertyName.StartsWith("getburst(0)."))
                    {
                        return GetBurstProperty(emission, propertyName.Substring("getburst(0).".Length));
                    }
                    return null;
            }
        }

        /// <summary>
        /// 获取Burst属性
        /// </summary>
        private object GetBurstProperty(ParticleSystem.EmissionModule emission, string propertyName)
        {
            // 安全检查：确保有至少一个burst
            if (emission.burstCount == 0)
            {
                ConversionLogger.LogWarning("尝试访问Burst[0]，但burstCount为0");
                return null;
            }

            try
            {
                var burst = emission.GetBurst(0);
                switch (propertyName.ToLower())
                {
                    case "time": return burst.time;
                    case "count.constant": return burst.count.constant;
                    case "cyclecount": return burst.cycleCount;
                    case "probability": return burst.probability;
                    default: return null;
                }
            }
            catch (System.Exception ex)
            {
                ConversionLogger.LogError($"获取Burst属性失败: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 获取Shape模块属性
        /// </summary>
        private object GetShapeModuleProperty(ParticleSystem.ShapeModule shape, string propertyName)
        {
            switch (propertyName.ToLower())
            {
                case "enabled": return shape.enabled;
                case "shapetype": return (int)shape.shapeType;
                case "angle": return shape.angle;
                case "radius": return shape.radius;
                default: return null;
            }
        }
        
        /// <summary>
        /// 获取VelocityOverLifetime模块属性
        /// </summary>
        private object GetVelocityOverLifetimeProperty(ParticleSystem.VelocityOverLifetimeModule module, string propertyName)
        {
            switch (propertyName.ToLower())
            {
                case "enabled": return module.enabled;
                case "space": return (int)module.space;
                default: return null;
            }
        }
        
        /// <summary>
        /// 获取ColorOverLifetime模块属性
        /// </summary>
        private object GetColorOverLifetimeProperty(ParticleSystem.ColorOverLifetimeModule module, string propertyName)
        {
            switch (propertyName.ToLower())
            {
                case "enabled": return module.enabled;
                default: return null;
            }
        }
        
        /// <summary>
        /// 获取SizeOverLifetime模块属性
        /// </summary>
        private object GetSizeOverLifetimeProperty(ParticleSystem.SizeOverLifetimeModule module, string propertyName)
        {
            switch (propertyName.ToLower())
            {
                case "enabled": return module.enabled;
                case "separateaxes": return module.separateAxes;
                default: return null;
            }
        }
        
        /// <summary>
        /// 获取RotationOverLifetime模块属性
        /// </summary>
        private object GetRotationOverLifetimeProperty(ParticleSystem.RotationOverLifetimeModule module, string propertyName)
        {
            switch (propertyName.ToLower())
            {
                case "enabled": return module.enabled;
                case "separateaxes": return module.separateAxes;
                default: return null;
            }
        }
        
        /// <summary>
        /// 获取Renderer属性
        /// </summary>
        private object GetRendererProperty(ParticleSystemRenderer renderer, string propertyName)
        {
            if (renderer == null) return null;
            
            switch (propertyName.ToLower())
            {
                case "rendermode": return (int)renderer.renderMode;
                case "mesh": return renderer.mesh != null ? renderer.mesh.name : "null";
                case "material": return renderer.material != null ? renderer.material.name : "null";
                case "sortingorder": return renderer.sortingOrder;
                default: return null;
            }
        }
        
        /// <summary>
        /// 获取其他模块属性（主要是enabled状态）
        /// </summary>
        private object GetOtherModuleProperty(ParticleSystem ps, string moduleName, string propertyName)
        {
            if (propertyName.ToLower() != "enabled") return null;
            
            switch (moduleName.ToLower())
            {
                case "limitvelocityoverlifetime": return ps.limitVelocityOverLifetime.enabled;
                case "inheritvelocity": return ps.inheritVelocity.enabled;
                case "forceoverlifetime": return ps.forceOverLifetime.enabled;
                case "colorbyspeed": return ps.colorBySpeed.enabled;
                case "sizebyspeed": return ps.sizeBySpeed.enabled;
                case "rotationbyspeed": return ps.rotationBySpeed.enabled;
                case "externalforces": return ps.externalForces.enabled;
                case "noise": return ps.noise.enabled;
                case "collision": return ps.collision.enabled;
                case "trigger": return ps.trigger.enabled;
                case "subemitters": return ps.subEmitters.enabled;
                case "texturesheetanimation": return ps.textureSheetAnimation.enabled;
                case "lights": return ps.lights.enabled;
                case "trails": return ps.trails.enabled;
                default: return null;
            }
        }
        
        /// <summary>
        /// 解析值
        /// </summary>
        private object ParseValue(string value)
        {
            value = value.Trim();
            
            // 布尔值
            if (bool.TryParse(value, out bool boolResult))
                return boolResult;
                
            // 整数
            if (int.TryParse(value, out int intResult))
                return intResult;
                
            // 浮点数
            if (float.TryParse(value, NumberStyles.Float, CultureInfo.InvariantCulture, out float floatResult))
                return floatResult;
                
            // 字符串（去除引号）
            if (value.StartsWith("\"") && value.EndsWith("\""))
                return value.Substring(1, value.Length - 2);
                
            // null值
            if (value.ToLower() == "null")
                return "null";
                
            return value;
        }
        
        /// <summary>
        /// 比较两个值
        /// </summary>
        private bool CompareValues(object left, object right, string op)
        {
            if (left == null || right == null)
            {
                switch (op)
                {
                    case "==": return left == right;
                    case "!=": return left != right;
                    default: return false;
                }
            }
            
            // 字符串比较
            if (left is string leftStr && right is string rightStr)
            {
                switch (op)
                {
                    case "==": return leftStr == rightStr;
                    case "!=": return leftStr != rightStr;
                    default: return false;
                }
            }
            
            // 布尔值比较
            if (left is bool leftBool && right is bool rightBool)
            {
                switch (op)
                {
                    case "==": return leftBool == rightBool;
                    case "!=": return leftBool != rightBool;
                    default: return false;
                }
            }
            
            // 数值比较
            if (IsNumeric(left) && IsNumeric(right))
            {
                double leftNum = Convert.ToDouble(left);
                double rightNum = Convert.ToDouble(right);
                
                switch (op)
                {
                    case "==": return Math.Abs(leftNum - rightNum) < 0.001; // 浮点数比较容差
                    case "!=": return Math.Abs(leftNum - rightNum) >= 0.001;
                    case ">": return leftNum > rightNum;
                    case "<": return leftNum < rightNum;
                    case ">=": return leftNum >= rightNum;
                    case "<=": return leftNum <= rightNum;
                    default: return false;
                }
            }
            
            return false;
        }
        
        /// <summary>
        /// 判断是否为数值类型
        /// </summary>
        private bool IsNumeric(object value)
        {
            return value is int || value is float || value is double || value is decimal;
        }
    }
}
