%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_AmbientEquatorColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_AmbientGroundColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 16
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 0
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0, g: 0, b: 0, a: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 11
  m_GIWorkflowMode: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 1
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 10
    m_Resolution: 2
    m_BakeResolution: 5
    m_AtlasSize: 2048
    m_AO: 1
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 15203, guid: 0000000000000000f000000000000000,
      type: 0}
    m_LightmapsBakeMode: 0
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 1
    m_MixedBakeMode: 1
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 4
    m_PVRSampleCount: 10
    m_PVRBounces: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVRFilteringMode: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ShowResolutionOverlay: 1
  m_LightingDataAsset: {fileID: 112000000, guid: ba32b3689ffd99c488acdbd1a0c0a3df,
    type: 2}
  m_UseShadowmask: 0
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &60102725 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 6055530383213968274, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &108711537 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7730980546693565141, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &109065213 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 3568389150483568372, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &109359491 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1504034668458520239, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &110366906 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1371247221995145637, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &181709058 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 5397286160319169270, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &196983827 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 4438393270394750702, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &218762945 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 2958316341781637638, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &246990900 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 550399354142416827, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &259208763 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 6587798106242927838, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &270999874 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 132293987615867172, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &296816393
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 296816394}
  m_Layer: 0
  m_Name: Click Point
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &296816394
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 296816393}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -6.549988, y: 0, z: -2.980011}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1709888414}
  m_RootOrder: 9
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &315255519 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8687010403445987672, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &318893735 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7777484332958683963, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &322358921 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7055847866747848029, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &387463117
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 387463118}
  m_Layer: 0
  m_Name: Click Point
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &387463118
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 387463117}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -7.7104187, y: 0, z: -4.9900055}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1709888414}
  m_RootOrder: 11
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &406208850 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 182213205716039762, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &412467866 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 5794461469378671955, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &425862350 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 616533018909246132, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &435192146 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8565449934148095553, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &546124001 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7618283188797911463, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &579250583 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 678446249506307073, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &612813468 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 4905802363688896967, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &645269097 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1919400253619199514, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &684079562 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1872224669934211047, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &698072279 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 67697255098191066, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &797548108 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7151237566193703227, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &812985219 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8495570973943877105, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &838010712
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 838010713}
  - component: {fileID: 838010715}
  - component: {fileID: 838010714}
  m_Layer: 0
  m_Name: Particle System
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &838010713
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 838010712}
  m_LocalRotation: {x: -0.7071068, y: -0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 6.49, y: 3.61, z: 6.69}
  m_Children: []
  m_Father: {fileID: 1596175733}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: -90, y: 0, z: 0}
--- !u!199 &838010714
ParticleSystemRenderer:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 838010712}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b825b8f17e6fb3d42a574f6bc88292df, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_RenderMode: 4
  m_SortMode: 0
  m_MinParticleSize: 0
  m_MaxParticleSize: 0.5
  m_CameraVelocityScale: 0
  m_VelocityScale: 0
  m_LengthScale: 2
  m_SortingFudge: 0
  m_NormalDirection: 1
  m_ShadowBias: 0
  m_RenderAlignment: 2
  m_Pivot: {x: 0, y: 0, z: 0}
  m_Flip: {x: 0, y: 0, z: 0}
  m_UseCustomVertexStreams: 0
  m_EnableGPUInstancing: 1
  m_ApplyActiveColorSpace: 1
  m_AllowRoll: 1
  m_VertexStreams: 00010304
  m_Mesh: {fileID: 4300000, guid: e0eceaff68e64534a8faf5d78e005a3b, type: 3}
  m_Mesh1: {fileID: 0}
  m_Mesh2: {fileID: 0}
  m_Mesh3: {fileID: 0}
  m_MaskInteraction: 0
--- !u!198 &838010715
ParticleSystem:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 838010712}
  serializedVersion: 6
  lengthInSec: 1
  simulationSpeed: 1
  stopAction: 0
  cullingMode: 0
  ringBufferMode: 0
  ringBufferLoopRange: {x: 0, y: 1}
  looping: 0
  prewarm: 0
  playOnAwake: 1
  useUnscaledTime: 0
  autoRandomSeed: 0
  useRigidbodyForVelocity: 1
  startDelay:
    serializedVersion: 2
    minMaxState: 0
    scalar: 0.4
    minScalar: 0
    maxCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    minCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  moveWithTransform: 0
  moveWithCustomTransform: {fileID: 0}
  scalingMode: 0
  randomSeed: 1680541533
  InitialModule:
    serializedVersion: 3
    enabled: 1
    startLifetime:
      serializedVersion: 2
      minMaxState: 0
      scalar: 2.4
      minScalar: 5
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startSpeed:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 5
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startColor:
      serializedVersion: 2
      minMaxState: 0
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 0.8301887, g: 0.30564985, b: 0.16838734, a: 0.5882353}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
    startSize:
      serializedVersion: 2
      minMaxState: 0
      scalar: 2
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startSizeY:
      serializedVersion: 2
      minMaxState: 0
      scalar: 2
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startSizeZ:
      serializedVersion: 2
      minMaxState: 0
      scalar: 3
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startRotationX:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startRotationY:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startRotation:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    randomizeRotationDirection: 0
    maxNumParticles: 1
    size3D: 1
    rotation3D: 0
    gravityModifier:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  ShapeModule:
    serializedVersion: 6
    enabled: 0
    type: 4
    angle: 25
    length: 5
    boxThickness: {x: 0, y: 0, z: 0}
    radiusThickness: 1
    donutRadius: 0.2
    m_Position: {x: 0, y: 0, z: 0}
    m_Rotation: {x: 0, y: 0, z: 0}
    m_Scale: {x: 1, y: 1, z: 1}
    placementMode: 0
    m_MeshMaterialIndex: 0
    m_MeshNormalOffset: 0
    m_MeshSpawn:
      mode: 0
      spread: 0
      speed:
        serializedVersion: 2
        minMaxState: 0
        scalar: 1
        minScalar: 1
        maxCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        minCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
    m_Mesh: {fileID: 0}
    m_MeshRenderer: {fileID: 0}
    m_SkinnedMeshRenderer: {fileID: 0}
    m_Sprite: {fileID: 0}
    m_SpriteRenderer: {fileID: 0}
    m_UseMeshMaterialIndex: 0
    m_UseMeshColors: 1
    alignToDirection: 0
    m_Texture: {fileID: 0}
    m_TextureClipChannel: 3
    m_TextureClipThreshold: 0
    m_TextureUVChannel: 0
    m_TextureColorAffectsParticles: 1
    m_TextureAlphaAffectsParticles: 1
    m_TextureBilinearFiltering: 0
    randomDirectionAmount: 0
    sphericalDirectionAmount: 0
    randomPositionAmount: 0
    radius:
      value: 1
      mode: 0
      spread: 0
      speed:
        serializedVersion: 2
        minMaxState: 0
        scalar: 1
        minScalar: 1
        maxCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        minCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
    arc:
      value: 360
      mode: 0
      spread: 0
      speed:
        serializedVersion: 2
        minMaxState: 0
        scalar: 1
        minScalar: 1
        maxCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        minCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
  EmissionModule:
    enabled: 1
    serializedVersion: 4
    rateOverTime:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 10
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    rateOverDistance:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    m_BurstCount: 1
    m_Bursts:
    - serializedVersion: 2
      time: 0
      countCurve:
        serializedVersion: 2
        minMaxState: 0
        scalar: 1
        minScalar: 30
        maxCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        minCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
      cycleCount: 1
      repeatInterval: 0.01
      probability: 1
  SizeModule:
    enabled: 0
    curve:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    z:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    separateAxes: 0
  RotationModule:
    enabled: 0
    x:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    curve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0.7853982
      minScalar: 0.7853982
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    separateAxes: 0
  ColorModule:
    enabled: 1
    gradient:
      serializedVersion: 2
      minMaxState: 1
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 0.97547174, g: 0.98620284, b: 1, a: 0}
        key1: {r: 0.97547174, g: 0.98620284, b: 1, a: 1}
        key2: {r: 0.52615935, g: 0, b: 0.78431374, a: 1}
        key3: {r: 0.4433962, g: 0, b: 0.28188145, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 48856
        ctime3: 65535
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 4241
        atime2: 52428
        atime3: 65535
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_NumColorKeys: 2
        m_NumAlphaKeys: 4
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
  UVModule:
    enabled: 0
    mode: 0
    timeMode: 0
    fps: 30
    frameOverTime:
      serializedVersion: 2
      minMaxState: 1
      scalar: 0.9999
      minScalar: 0.9999
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startFrame:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    speedRange: {x: 0, y: 1}
    tilesX: 1
    tilesY: 1
    animationType: 0
    rowIndex: 0
    cycles: 1
    uvChannelMask: -1
    randomRow: 1
    sprites:
    - sprite: {fileID: 0}
    flipU: 0
    flipV: 0
  VelocityModule:
    enabled: 0
    x:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    z:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalX:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalY:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalZ:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalOffsetX:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalOffsetY:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalOffsetZ:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    radial:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    speedModifier:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    inWorldSpace: 0
  InheritVelocityModule:
    enabled: 0
    m_Mode: 0
    m_Curve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  ForceModule:
    enabled: 0
    x:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    z:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    inWorldSpace: 0
    randomizePerFrame: 0
  ExternalForcesModule:
    enabled: 0
    multiplier: 1
    influenceFilter: 0
    influenceMask:
      serializedVersion: 2
      m_Bits: 4294967295
    influenceList: []
  ClampVelocityModule:
    enabled: 0
    x:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    z:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    magnitude:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    separateAxis: 0
    inWorldSpace: 0
    multiplyDragByParticleSize: 1
    multiplyDragByParticleVelocity: 1
    dampen: 0
    drag:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  NoiseModule:
    enabled: 0
    strength:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    strengthY:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    strengthZ:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    separateAxes: 0
    frequency: 0.5
    damping: 1
    octaves: 1
    octaveMultiplier: 0.5
    octaveScale: 2
    quality: 2
    scrollSpeed:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    remap:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    remapY:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    remapZ:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    remapEnabled: 0
    positionAmount:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    rotationAmount:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    sizeAmount:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  SizeBySpeedModule:
    enabled: 0
    curve:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    z:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    range: {x: 0, y: 1}
    separateAxes: 0
  RotationBySpeedModule:
    enabled: 0
    x:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    curve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0.7853982
      minScalar: 0.7853982
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    separateAxes: 0
    range: {x: 0, y: 1}
  ColorBySpeedModule:
    enabled: 0
    gradient:
      serializedVersion: 2
      minMaxState: 1
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
    range: {x: 0, y: 1}
  CollisionModule:
    enabled: 0
    serializedVersion: 3
    type: 0
    collisionMode: 0
    colliderForce: 0
    multiplyColliderForceByParticleSize: 0
    multiplyColliderForceByParticleSpeed: 0
    multiplyColliderForceByCollisionAngle: 1
    plane0: {fileID: 0}
    plane1: {fileID: 0}
    plane2: {fileID: 0}
    plane3: {fileID: 0}
    plane4: {fileID: 0}
    plane5: {fileID: 0}
    m_Dampen:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    m_Bounce:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    m_EnergyLossOnCollision:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    minKillSpeed: 0
    maxKillSpeed: 10000
    radiusScale: 1
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    maxCollisionShapes: 256
    quality: 0
    voxelSize: 0.5
    collisionMessages: 0
    collidesWithDynamic: 1
    interiorCollisions: 0
  TriggerModule:
    enabled: 0
    collisionShape0: {fileID: 0}
    collisionShape1: {fileID: 0}
    collisionShape2: {fileID: 0}
    collisionShape3: {fileID: 0}
    collisionShape4: {fileID: 0}
    collisionShape5: {fileID: 0}
    inside: 1
    outside: 0
    enter: 0
    exit: 0
    radiusScale: 1
  SubModule:
    serializedVersion: 2
    enabled: 0
    subEmitters:
    - serializedVersion: 3
      emitter: {fileID: 0}
      type: 0
      properties: 0
      emitProbability: 1
  LightsModule:
    enabled: 0
    ratio: 0
    light: {fileID: 0}
    randomDistribution: 1
    color: 1
    range: 1
    intensity: 1
    rangeCurve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    intensityCurve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    maxLights: 20
  TrailModule:
    enabled: 0
    mode: 0
    ratio: 1
    lifetime:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    minVertexDistance: 0.2
    textureMode: 0
    ribbonCount: 1
    shadowBias: 0.5
    worldSpace: 0
    dieWithParticles: 1
    sizeAffectsWidth: 1
    sizeAffectsLifetime: 0
    inheritParticleColor: 1
    generateLightingData: 0
    splitSubEmitterRibbons: 0
    attachRibbonsToTransform: 0
    colorOverLifetime:
      serializedVersion: 2
      minMaxState: 0
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
    widthOverTrail:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    colorOverTrail:
      serializedVersion: 2
      minMaxState: 0
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
  CustomDataModule:
    enabled: 0
    mode0: 0
    vectorComponentCount0: 4
    color0:
      serializedVersion: 2
      minMaxState: 0
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
    colorLabel0: Color
    vector0_0:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel0_0: X
    vector0_1:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel0_1: Y
    vector0_2:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel0_2: Z
    vector0_3:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel0_3: W
    mode1: 0
    vectorComponentCount1: 4
    color1:
      serializedVersion: 2
      minMaxState: 0
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
    colorLabel1: Color
    vector1_0:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel1_0: X
    vector1_1:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel1_1: Y
    vector1_2:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel1_2: Z
    vector1_3:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel1_3: W
--- !u!1 &838343343
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 838343345}
  - component: {fileID: 838343344}
  m_Layer: 0
  m_Name: GfxManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &838343344
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 838343343}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 91b8ad55b853622448b29257dd5b3d0c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &838343345
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 838343343}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &844671845 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 5568348435886237378, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &900863517 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 502665234622275642, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &936466416 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 896976678702156299, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &944652439 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 566719031810321387, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &965369704 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 2643104277598695724, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &979413324 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7094480957047110698, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &985092868 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8714565258725583782, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1041296529
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1076223608738190436, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3280952182863430971, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_StaticEditorFlags
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3971730519847772778, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3971730519847772778, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3971730519847772778, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3971730519847772778, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3971730519847772778, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3971730519847772778, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3971730519847772778, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3971730519847772778, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_RootOrder
      value: 6
      objectReference: {fileID: 0}
    - target: {fileID: 3971730519847772778, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3971730519847772778, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3971730519847772778, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6736554696865127465, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_Name
      value: s4_tft_default_001
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMaps.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.size
      value: 70
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMaps.Array.data[0].lightmapColor
      value: 
      objectReference: {fileID: 2800000, guid: 1730b8701cd75f64cb55d7a9e0df0a79, type: 3}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMaps.Array.data[0].lightmapDir
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMaps.Array.data[1].lightmapColor
      value: 
      objectReference: {fileID: 2800000, guid: dc6cdd70bf06316468576f485e120f3c, type: 3}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMaps.Array.data[1].lightmapDir
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMaps.Array.data[2].lightmapColor
      value: 
      objectReference: {fileID: 2800000, guid: e6f81a164ca45774bb2f5016e82761c9, type: 3}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMaps.Array.data[2].lightmapDir
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMaps.Array.data[3].lightmapColor
      value: 
      objectReference: {fileID: 2800000, guid: 28156438250de35408da77326ab27236, type: 3}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMaps.Array.data[3].lightmapDir
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMaps.Array.data[4].lightmapColor
      value: 
      objectReference: {fileID: 2800000, guid: 1f35593539f6ab242abb1f023a775895, type: 3}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMaps.Array.data[4].lightmapDir
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMaps.Array.data[5].lightmapColor
      value: 
      objectReference: {fileID: 2800000, guid: 4ccc663c872cfef4e890aa94da38d951, type: 3}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMaps.Array.data[5].lightmapDir
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMaps.Array.data[6].lightmapColor
      value: 
      objectReference: {fileID: 2800000, guid: c44c8438ba9096341b9ec887ddf014e0, type: 3}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMaps.Array.data[6].lightmapDir
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[43].lightMapIndex
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[43].lightmapScaleOffset.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[43].lightmapScaleOffset.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[43].lightmapScaleOffset.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[43].lightmapScaleOffset.w
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[45].lightMapIndex
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[45].lightmapScaleOffset.x
      value: 0.29248047
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[45].lightmapScaleOffset.y
      value: 0.29248047
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[45].lightmapScaleOffset.z
      value: 1.6235928e-17
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[46].lightMapIndex
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[46].lightmapScaleOffset.x
      value: 0.22998047
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[46].lightmapScaleOffset.y
      value: 0.22998047
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[46].lightmapScaleOffset.z
      value: 0.29327732
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[46].lightmapScaleOffset.w
      value: 0.24902344
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[47].lightMapIndex
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[47].lightmapScaleOffset.x
      value: 0.28466797
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[47].lightmapScaleOffset.y
      value: 0.28466797
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[47].lightmapScaleOffset.w
      value: 0.29345703
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[48].lightMapIndex
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[48].lightmapScaleOffset.x
      value: 0.25195312
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[48].lightmapScaleOffset.y
      value: 0.25195312
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[48].lightmapScaleOffset.w
      value: 0.5790247
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[49].lightMapIndex
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[49].lightmapScaleOffset.x
      value: 0.17919922
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[49].lightmapScaleOffset.y
      value: 0.17919922
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[49].lightmapScaleOffset.z
      value: 0.7860046
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[49].lightmapScaleOffset.w
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[50].lightMapIndex
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[50].lightmapScaleOffset.x
      value: 0.24804688
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[50].lightmapScaleOffset.y
      value: 0.24804688
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[50].lightmapScaleOffset.z
      value: 0.29327732
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[51].lightmapScaleOffset.x
      value: 0.24511719
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[51].lightmapScaleOffset.y
      value: 0.24511719
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[51].lightmapScaleOffset.z
      value: 0.53991085
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[0]
      value: 
      objectReference: {fileID: 110366906}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[1]
      value: 
      objectReference: {fileID: 1198348313}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[2]
      value: 
      objectReference: {fileID: 109359491}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[3]
      value: 
      objectReference: {fileID: 1268618089}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[4]
      value: 
      objectReference: {fileID: 109065213}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[5]
      value: 
      objectReference: {fileID: 2007426249}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[6]
      value: 
      objectReference: {fileID: 1824189949}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[7]
      value: 
      objectReference: {fileID: 1964837706}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[8]
      value: 
      objectReference: {fileID: 1115348574}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[9]
      value: 
      objectReference: {fileID: 1951229011}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[10]
      value: 
      objectReference: {fileID: 812985219}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[11]
      value: 
      objectReference: {fileID: 259208763}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[12]
      value: 
      objectReference: {fileID: 181709058}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[13]
      value: 
      objectReference: {fileID: 246990900}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[14]
      value: 
      objectReference: {fileID: 698072279}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[15]
      value: 
      objectReference: {fileID: 1391309490}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[16]
      value: 
      objectReference: {fileID: 1071028621}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[17]
      value: 
      objectReference: {fileID: 797548108}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[18]
      value: 
      objectReference: {fileID: 2031515485}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[19]
      value: 
      objectReference: {fileID: 1666419675}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[20]
      value: 
      objectReference: {fileID: 979413324}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[21]
      value: 
      objectReference: {fileID: 218762945}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[22]
      value: 
      objectReference: {fileID: 900863517}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[23]
      value: 
      objectReference: {fileID: 318893735}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[24]
      value: 
      objectReference: {fileID: 1689677750}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[25]
      value: 
      objectReference: {fileID: 1052150597}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[26]
      value: 
      objectReference: {fileID: 1169619394}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[27]
      value: 
      objectReference: {fileID: 1275103210}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[28]
      value: 
      objectReference: {fileID: 1901169930}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[29]
      value: 
      objectReference: {fileID: 2013547333}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[30]
      value: 
      objectReference: {fileID: 270999874}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[31]
      value: 
      objectReference: {fileID: 196983827}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[32]
      value: 
      objectReference: {fileID: 1673993932}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[33]
      value: 
      objectReference: {fileID: 425862350}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[34]
      value: 
      objectReference: {fileID: 322358921}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[35]
      value: 
      objectReference: {fileID: 1128330801}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[36]
      value: 
      objectReference: {fileID: 985092868}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[37]
      value: 
      objectReference: {fileID: 412467866}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[38]
      value: 
      objectReference: {fileID: 546124001}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[39]
      value: 
      objectReference: {fileID: 579250583}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[40]
      value: 
      objectReference: {fileID: 435192146}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[41]
      value: 
      objectReference: {fileID: 965369704}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[42]
      value: 
      objectReference: {fileID: 2111744236}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[43]
      value: 
      objectReference: {fileID: 2016951862}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[44]
      value: 
      objectReference: {fileID: 936466416}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[45]
      value: 
      objectReference: {fileID: 2132103775}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[46]
      value: 
      objectReference: {fileID: 1621769663}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[47]
      value: 
      objectReference: {fileID: 944652439}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[48]
      value: 
      objectReference: {fileID: 60102725}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[49]
      value: 
      objectReference: {fileID: 1360370822}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[50]
      value: 
      objectReference: {fileID: 2015651809}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[51]
      value: 
      objectReference: {fileID: 1993884079}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[52]
      value: 
      objectReference: {fileID: 1848424844}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[53]
      value: 
      objectReference: {fileID: 315255519}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[54]
      value: 
      objectReference: {fileID: 645269097}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[55]
      value: 
      objectReference: {fileID: 1317673106}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[56]
      value: 
      objectReference: {fileID: 1903026681}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[57]
      value: 
      objectReference: {fileID: 1615474548}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[58]
      value: 
      objectReference: {fileID: 1364645908}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[59]
      value: 
      objectReference: {fileID: 1287839925}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[60]
      value: 
      objectReference: {fileID: 1506472171}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[61]
      value: 
      objectReference: {fileID: 406208850}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[62]
      value: 
      objectReference: {fileID: 612813468}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[63]
      value: 
      objectReference: {fileID: 844671845}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[64]
      value: 
      objectReference: {fileID: 684079562}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[65]
      value: 
      objectReference: {fileID: 1948304737}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[66]
      value: 
      objectReference: {fileID: 1543810567}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[67]
      value: 
      objectReference: {fileID: 108711537}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[68]
      value: 
      objectReference: {fileID: 2138872312}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_bakeLights.Array.data[69]
      value: 
      objectReference: {fileID: 2144507441}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightmapsMode
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7853477259380440510, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
        type: 3}
      propertyPath: m_dynamicLightMapModule.m_lightMapRendererInfos.Array.data[51].lightMapIndex
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 9a3e3e67f2c254349a9eb24dff65f7d4, type: 3}
--- !u!1 &1052150597 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 4046446328224201734, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1071028621 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 426716849546582936, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1115348574 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 3345276110670984727, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1128330801 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 3966634798916261223, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1169619394 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1701524383164209984, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1175861792
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1175861793}
  m_Layer: 0
  m_Name: Click Point
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1175861793
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1175861792}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -5.389557, y: 0, z: 3.0499725}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1709888414}
  m_RootOrder: 10
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1192085045 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7664021469314200369, guid: c64816a5012a713469723331e0e06397,
    type: 3}
  m_PrefabInstance: {fileID: **********}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1192085047
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1192085045}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: a3aa38a035d3e2d43be2dc7bf79e9ea2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  prefab: {fileID: 0}
--- !u!114 &1192085048
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1192085045}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 88c2a24f2b95086458ce40939c5242a0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1198348313 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 5235005818782023908, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1268618089 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 6142539248778537366, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1275103210 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 2826415799034357048, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1287839925 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8727416443869344630, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1317673106 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 5641491715336980436, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1358680802
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1358680804}
  - component: {fileID: 1358680803}
  m_Layer: 0
  m_Name: WwiseGlobal
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1358680803
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1358680802}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 0dade76715f13934dbdeeef35a8094e6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  InitializationSettings: {fileID: 11400000, guid: be46ed01d7dc3df45bae83365a95063d,
    type: 2}
  basePath: 
  language: 
  defaultPoolSize: 0
  lowerPoolSize: 0
  streamingPoolSize: 0
  memoryCutoffThreshold: 0
  monitorPoolSize: 0
  monitorQueuePoolSize: 0
  callbackManagerBufferSize: 0
  spatialAudioPoolSize: 0
  maxSoundPropagationDepth: 0
  diffractionFlags: 11
  engineLogging: 0
--- !u!4 &1358680804
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1358680802}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1360370822 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 6719093556689626836, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1364645908 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 3238376957142599975, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1391309490 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 3862989729817152385, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1414340990
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1414340991}
  m_Layer: 0
  m_Name: Click Point
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1414340991
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1414340990}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -6.549988, y: 0, z: -2.980011}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1709888414}
  m_RootOrder: 8
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1506472171 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7071288297022299298, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1543810567 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 2609811869583838644, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1596175731
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1596175733}
  - component: {fileID: 1596175732}
  m_Layer: 0
  m_Name: GameObject
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1596175732
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1596175731}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 4d03f23b79cf75341a08752879387537, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_sortingOrder: 0
  m_dynamicLevel: 1
  m_playFinishedHide: 0
  m_timeSystem:
    m_startTime: 0
    m_endTime: 1
    m_loop: 0
    m_timeScale: 1
  m_gfxNodeArray:
  - m_transform: {fileID: 838010713}
    m_lodSystem:
      m_lod: 1
      m_tags: []
  m_enableOptimize: 1
  abName: 
  assetName: 
  m_isNewCompressData: 0
--- !u!4 &1596175733
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1596175731}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2.5805209, y: 3.079018, z: 0.42520267}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 838010713}
  m_Father: {fileID: 0}
  m_RootOrder: 8
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1615474548 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 9222271471858866134, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1621769663 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 5206189747976313136, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1666419675 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7640986302026536090, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1673993932 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 664916917694087950, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1689677750 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 3425475598560051154, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1700176607
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1700176609}
  - component: {fileID: 1700176608}
  m_Layer: 0
  m_Name: BattleMapManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1700176608
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1700176607}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: d2eea79286ab32d43885cde1f6bb958d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_gameDynamicRoot: {fileID: 1192085045}
  m_roundSelectDynamicRoot: {fileID: 0}
  RoundSelectLODEffect: []
--- !u!4 &1700176609
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1700176607}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &1709888414 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7664021470395307922, guid: c64816a5012a713469723331e0e06397,
    type: 3}
  m_PrefabInstance: {fileID: **********}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1824189949 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 5806659324384296392, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1848424844 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 4981737783966828594, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &**********
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1878980313}
  - component: {fileID: 1878980312}
  - component: {fileID: **********}
  m_Layer: 0
  m_Name: "\u63A7\u5236\u4E2D\u5FC3"
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1878980312
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: **********}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 50c12fbf9c1f5b24694a3186e6ed681d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_attackerGo: {fileID: 6100882763811202898, guid: 1abc3a3f6e8790f45af27995442c5c53,
    type: 3}
  m_defenerGo: {fileID: 6100882763811202898, guid: 1abc3a3f6e8790f45af27995442c5c53,
    type: 3}
  m_attackConfig: {fileID: 0}
  m_attackConfigBin: {fileID: 0}
  m_attackerPos: {x: -6.59, y: 0.6, z: -4.9}
  m_defenderPos: {x: 6.32, y: 0.6, z: 4}
  AttackEffectSelectIndex: 0
  AttackEffectNameList:
  - "\u3010\u4E3B\u3011RunAttack \u653B\u51FB\u7279\u6548 0"
  - "Bullet \u653B\u51FB\u7279\u6548 1"
  - "Bullet \u653B\u51FB\u7279\u6548 2"
  - "\u7B2C\u4E00\u6B21\u5B50\u5F39"
  - "\u98DE\u51FA\u53BB\u7684\u5B50\u5F39"
  - "\u97F3\u6548\u7528"
  m_attackBig: 0
--- !u!4 &1878980313
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: **********}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &**********
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: **********}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 39dc4e4c78d96c84e843fc9c5dc05c67, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  soundType: 2
  isThreeD: 0
  bankName: 
  soundName: 
  EndbankName: 
  EndsoundName: 
  disableStopSound: 1
  playCompeleteDisable: 0
  FirstNoPlay: 0
  hasClosed: 0
--- !u!1 &********** stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7664021470287443117, guid: c64816a5012a713469723331e0e06397,
    type: 3}
  m_PrefabInstance: {fileID: **********}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &**********
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: **********}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 72ece51f2901e7445ab60da3685d6b5f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ShowDebugText: 1
  m_ShowCameraFrustum: 1
  m_IgnoreTimeScale: 0
  m_WorldUpOverride: {fileID: 0}
  m_VcamUpdateMethod: 2
  m_BrainUpdateMethod: 1
  m_DefaultBlend:
    m_Style: 1
    m_Time: 2
    m_CustomCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  m_CustomBlends: {fileID: 0}
  m_CameraCutEvent:
    m_PersistentCalls:
      m_Calls: []
  m_CameraActivatedEvent:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1888572282
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: **********}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 583291ffdaa841140ba05a2d5abeb8fe, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  isDefaultListener: 1
  listenerId: 0
--- !u!114 &1888572283
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: **********}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 3a8ecba1469e1964495f4894b304dc97, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_listeners:
    initialListenerList: []
    useDefaultListeners: 1
  isEnvironmentAware: 0
  isStaticObject: 0
  m_positionOffsetData:
    KeepMe: 0
    positionOffset: {x: 0, y: 0, z: 0}
  cacheTransform: {fileID: 1984353695}
  m_posOffsetData: {fileID: 0}
  listenerMask: 1
--- !u!1 &1901169930 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 698028903289461666, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1903026681 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 253427410095225376, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1948304737 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8632417253295152804, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1951229011 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 9077992829705791280, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1964837706 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 4344502982996811631, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1975461808
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 141023520693953132, guid: 92371339708e18344916218038cae97d,
        type: 3}
      propertyPath: m_Materials.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2172487696892381850, guid: 92371339708e18344916218038cae97d,
        type: 3}
      propertyPath: InitialModule.size3D
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2172487696892381850, guid: 92371339708e18344916218038cae97d,
        type: 3}
      propertyPath: InitialModule.rotation3D
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5172799372745593054, guid: 92371339708e18344916218038cae97d,
        type: 3}
      propertyPath: m_Materials.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8577575817148560657, guid: 92371339708e18344916218038cae97d,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8577575817148560657, guid: 92371339708e18344916218038cae97d,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8577575817148560657, guid: 92371339708e18344916218038cae97d,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8577575817148560657, guid: 92371339708e18344916218038cae97d,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8577575817148560657, guid: 92371339708e18344916218038cae97d,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8577575817148560657, guid: 92371339708e18344916218038cae97d,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8577575817148560657, guid: 92371339708e18344916218038cae97d,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8577575817148560657, guid: 92371339708e18344916218038cae97d,
        type: 3}
      propertyPath: m_RootOrder
      value: 7
      objectReference: {fileID: 0}
    - target: {fileID: 8577575817148560657, guid: 92371339708e18344916218038cae97d,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8577575817148560657, guid: 92371339708e18344916218038cae97d,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8577575817148560657, guid: 92371339708e18344916218038cae97d,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8577575817148560668, guid: 92371339708e18344916218038cae97d,
        type: 3}
      propertyPath: m_timeSystem.m_loop
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8577575817148560669, guid: 92371339708e18344916218038cae97d,
        type: 3}
      propertyPath: m_Name
      value: t_yoneboyband_1_click01_ground_loc
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 92371339708e18344916218038cae97d, type: 3}
--- !u!1 &1975461809 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8577575815164668160, guid: 92371339708e18344916218038cae97d,
    type: 3}
  m_PrefabInstance: {fileID: 1975461808}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1975461810
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1975461809}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 0a8da32d9c7e6af4aa0e9032cc297937, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_timeSystem:
    m_startTime: 0
    m_endTime: 1
    m_loop: 0
    m_timeScale: 1
  m_translateModule:
    m_enable: 1
    m_worldSpace: 1
    m_xCurve:
      m_enable: 1
      m_curveType: 1
      m_curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      m_speed: 0
    m_yCurve:
      m_enable: 0
      m_curveType: 1
      m_curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      m_speed: 0
    m_zCurve:
      m_enable: 0
      m_curveType: 1
      m_curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      m_speed: 0
  m_rotModule:
    m_enable: 1
    m_alwaysFaceCamera: 0
    m_faceCameraInfluenceAxis: 0
    m_worldSpace: 1
    m_faceCameraAxis: 0
    m_xCurve:
      m_enable: 0
      m_curveType: 1
      m_curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      m_speed: 0
    m_yCurve:
      m_enable: 0
      m_curveType: 1
      m_curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      m_speed: 0
    m_zCurve:
      m_enable: 0
      m_curveType: 1
      m_curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      m_speed: 0
  m_scaleModule:
    m_enable: 0
    m_uniformScale: 1
    m_xCurve:
      m_enable: 0
      m_curveType: 1
      m_curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      m_speed: 0
    m_yCurve:
      m_enable: 0
      m_curveType: 1
      m_curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      m_speed: 0
    m_zCurve:
      m_enable: 0
      m_curveType: 1
      m_curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      m_speed: 0
  m_activeModule:
    m_enable: 0
    m_activePercent: 0
    m_disactivePercent: 1
--- !u!1001 &**********
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 3343699778627058682, guid: c64816a5012a713469723331e0e06397,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7664021469314200369, guid: c64816a5012a713469723331e0e06397,
        type: 3}
      propertyPath: m_Name
      value: game_dynamic_battle
      objectReference: {fileID: 0}
    - target: {fileID: 7664021470287443116, guid: c64816a5012a713469723331e0e06397,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7664021470287443116, guid: c64816a5012a713469723331e0e06397,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7664021470287443120, guid: c64816a5012a713469723331e0e06397,
        type: 3}
      propertyPath: hdrBloom.enabled
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7664021470287443120, guid: c64816a5012a713469723331e0e06397,
        type: 3}
      propertyPath: hdrBloom.intensity
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7664021470287443120, guid: c64816a5012a713469723331e0e06397,
        type: 3}
      propertyPath: usingHDR
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7664021470287443121, guid: c64816a5012a713469723331e0e06397,
        type: 3}
      propertyPath: near clip plane
      value: 0.1
      objectReference: {fileID: 0}
    - target: {fileID: 7664021470287443121, guid: c64816a5012a713469723331e0e06397,
        type: 3}
      propertyPath: far clip plane
      value: 1000
      objectReference: {fileID: 0}
    - target: {fileID: 7664021470287443121, guid: c64816a5012a713469723331e0e06397,
        type: 3}
      propertyPath: field of view
      value: 29
      objectReference: {fileID: 0}
    - target: {fileID: 7664021470287443121, guid: c64816a5012a713469723331e0e06397,
        type: 3}
      propertyPath: m_HDR
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8592922349593743019, guid: c64816a5012a713469723331e0e06397,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8592922349593743019, guid: c64816a5012a713469723331e0e06397,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8592922349593743019, guid: c64816a5012a713469723331e0e06397,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8592922349593743019, guid: c64816a5012a713469723331e0e06397,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8592922349593743019, guid: c64816a5012a713469723331e0e06397,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8592922349593743019, guid: c64816a5012a713469723331e0e06397,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8592922349593743019, guid: c64816a5012a713469723331e0e06397,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8592922349593743019, guid: c64816a5012a713469723331e0e06397,
        type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8592922349593743019, guid: c64816a5012a713469723331e0e06397,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8592922349593743019, guid: c64816a5012a713469723331e0e06397,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8592922349593743019, guid: c64816a5012a713469723331e0e06397,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: c64816a5012a713469723331e0e06397, type: 3}
--- !u!4 &1984353695 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7664021470287443116, guid: c64816a5012a713469723331e0e06397,
    type: 3}
  m_PrefabInstance: {fileID: **********}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1993884079 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8643829125125223524, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &2007426249 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 9203054886516858253, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &2013547333 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 2634024931049854867, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &2015651809 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8389726164796758738, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &2016951862 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8105661470990106257, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &2031515485 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8259687954715362599, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &2063525197
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2063525200}
  - component: {fileID: 2063525199}
  - component: {fileID: 2063525198}
  m_Layer: 0
  m_Name: EventSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2063525198
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063525197}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1077351063, guid: f70555f144d8491a825f0804e09c671c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_HorizontalAxis: Horizontal
  m_VerticalAxis: Vertical
  m_SubmitButton: Submit
  m_CancelButton: Cancel
  m_InputActionsPerSecond: 10
  m_RepeatDelay: 0.5
  m_ForceModuleActive: 0
--- !u!114 &2063525199
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063525197}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -619905303, guid: f70555f144d8491a825f0804e09c671c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FirstSelected: {fileID: 0}
  m_sendNavigationEvents: 1
  m_DragThreshold: 10
--- !u!4 &2063525200
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063525197}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2111744236 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 4544461345467743643, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &2132103775 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 2058020693401775623, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &2138872312 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 6105172238651729131, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &2144507441 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 42907596254611870, guid: 9a3e3e67f2c254349a9eb24dff65f7d4,
    type: 3}
  m_PrefabInstance: {fileID: 1041296529}
  m_PrefabAsset: {fileID: 0}
